# Chat Flutter Example

这是一个Flutter聊天应用示例，展示了如何使用chat_flutter插件构建功能完整的聊天应用。

## 功能特性

### 聊天会话列表页面
- 显示所有聊天会话的列表
- 每个会话项显示头像、会话标题、最后一条消息预览和时间
- 支持未读消息数量显示
- 点击会话项可进入对应的聊天页面
- 支持创建新的聊天会话

### 聊天页面
支持三种消息类型的显示和渲染：

1. **系统提示消息**
   - 文本居中对齐，使用灰色字体颜色
   - 用于显示系统通知或提示信息

2. **插画消息**
   - 图片居中显示，专门用于故事插图展示
   - 点击图片后可以放大查看（图片预览功能）
   - 支持图片描述文字显示

3. **普通文字消息**
   - 标准的聊天气泡样式
   - 区分发送者和接收者的消息显示
   - 用户消息显示在右侧，AI助手消息显示在左侧

### 其他功能
- 消息输入框和发送功能
- 消息列表支持滚动查看历史消息
- 适配不同屏幕尺寸
- 会话重命名功能
- 会话删除功能
- 集成AI聊天API

## 项目结构

```
lib/
├── main.dart                    # 应用入口
├── models/                      # 数据模型
│   ├── message.dart            # 消息数据模型
│   └── chat_session.dart       # 聊天会话数据模型
├── pages/                       # 页面
│   ├── chat_session_list_page.dart  # 聊天会话列表页面
│   └── chat_page.dart          # 聊天页面
├── widgets/                     # UI组件
│   ├── message_widget.dart     # 消息组件
│   └── image_preview_dialog.dart   # 图片预览对话框
├── network/                     # 网络相关
│   ├── api.dart                # API接口
│   └── entities/               # 网络实体类
└── ui/
    └── design_spec.dart        # 设计规范
```

## 使用方法

1. 启动应用后，点击底部导航栏的"Chat"标签
2. 在聊天会话列表页面，可以查看所有聊天记录
3. 点击右上角的"+"按钮创建新的聊天会话
4. 点击任意会话项进入聊天页面
5. 在聊天页面中可以发送消息，查看AI回复
6. 点击图片消息可以放大预览
7. 点击右上角的菜单可以重命名或删除会话

## 技术实现

- 使用Flutter框架开发
- 遵循Material Design设计规范
- 实现了完整的消息数据模型
- 集成了AI聊天API（SiliconFlow）
- 支持图片加载和预览
- 实现了响应式UI设计

## 测试

项目包含了完整的单元测试，测试覆盖了：
- 消息模型的创建和序列化
- 聊天会话模型的功能
- 数据的JSON序列化和反序列化

运行测试：
```bash
flutter test
```

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
