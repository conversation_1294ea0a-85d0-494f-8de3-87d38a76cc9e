import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../ui/design_spec.dart';
import '../models/user_profile.dart';
import '../services/user_profile_service.dart';
import 'avatar_edit_page.dart';
import 'nickname_edit_page.dart';
import 'language_settings_page.dart';
import 'webview_page.dart';

class ProfilePage extends StatefulWidget {
  final Function(Locale)? onLanguageChanged;

  const ProfilePage({
    super.key,
    this.onLanguageChanged,
  });

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  UserProfile? _userProfile;
  UserProfileService? _profileService;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeProfile();
  }

  Future<void> _initializeProfile() async {
    try {
      _profileService = await UserProfileService.getInstance();
      _userProfile = _profileService!.getCurrentProfile();
    } catch (e) {
      debugPrint('初始化用户配置失败: $e');
      _userProfile = UserProfile.defaultProfile();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _refreshProfile() async {
    if (_profileService != null) {
      setState(() {
        _userProfile = _profileService!.getCurrentProfile();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      backgroundColor: DesignSpec.primaryBackground,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 30),
              _buildProfileSection(),
              const SizedBox(height: 30),
              _buildSettingsSection(),
              const SizedBox(height: 30),
              _buildAboutSection(),
              const SizedBox(height: 100), // 为底部导航留出空间
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Text(
      AppLocalizations.of(context)!.profile,
      style: TextStyle(
        fontSize: DesignSpec.fontSize3Xl,
        fontWeight: DesignSpec.fontWeightBold,
        color: DesignSpec.primaryItemSelected,
      ),
    );
  }

  Widget _buildProfileSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _buildUserInfoRow(),
    );
  }

  Widget _buildUserInfoRow() {
    return Row(
      children: [
        // 头像区域，带有右下角编辑按钮
        Stack(
          children: [
            CircleAvatar(
              radius: 35,
              backgroundColor: DesignSpec.primaryItemUnselected.withOpacity(0.2),
              child: _userProfile?.avatarPath.isNotEmpty == true
                  ? ClipOval(
                      child: Image.asset(
                        _userProfile!.avatarPath,
                        width: 70,
                        height: 70,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            Icons.person,
                            size: 35,
                            color: DesignSpec.primaryItemUnselected,
                          );
                        },
                      ),
                    )
                  : Icon(
                      Icons.person,
                      size: 35,
                      color: DesignSpec.primaryItemUnselected,
                    ),
            ),
            // 头像编辑按钮 - 右下角叠加
            Positioned(
              right: 0,
              bottom: 0,
              child: GestureDetector(
                onTap: () => _navigateToAvatarEdit(),
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: DesignSpec.primaryItemSelected,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: DesignSpec.secondaryBackground,
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.edit,
                    size: 12,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(width: 20),
        // 用户信息区域
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 昵称行
              Row(
                children: [
                  Text(
                    _userProfile?.nickname ?? 'User',
                    style: TextStyle(
                      fontSize: DesignSpec.fontSizeLg,
                      fontWeight: DesignSpec.fontWeightMedium,
                    ),
                  ),
                  // 昵称编辑按钮
                  GestureDetector(
                    onTap: () => _navigateToNicknameEdit(),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      child: Icon(
                        Icons.edit,
                        size: 16,
                        color: DesignSpec.primaryItemSelected,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // 性别信息
              Row(
                children: [
                  Icon(
                    _getGenderIcon(),
                    size: 16,
                    color: DesignSpec.primaryItemUnselected,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    _getGenderDisplayText(),
                    style: TextStyle(
                      fontSize: DesignSpec.fontSizeSm,
                      color: DesignSpec.primaryItemUnselected,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getGenderIcon() {
    switch (_userProfile?.gender) {
      case Gender.male:
        return Icons.male;
      case Gender.female:
        return Icons.female;
      default:
        return Icons.person_outline;
    }
  }

  String _getGenderDisplayText() {
    switch (_userProfile?.gender) {
      case Gender.male:
        return AppLocalizations.of(context)!.male;
      case Gender.female:
        return AppLocalizations.of(context)!.female;
      default:
        return AppLocalizations.of(context)!.unspecified;
    }
  }

  Widget _buildSettingsSection() {
    return Container(
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildSettingItem(
            icon: Icons.language,
            title: AppLocalizations.of(context)!.languageSettings,
            onTap: () => _navigateToLanguageSettings(),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection() {
    return Container(
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildSettingItem(
            icon: Icons.privacy_tip_outlined,
            title: AppLocalizations.of(context)!.privacyPolicy,
            onTap: () => _navigateToWebView(
              title: AppLocalizations.of(context)!.privacyPolicy,
              url: 'https://example.com/privacy-policy',
            ),
          ),
          const Divider(height: 1),
          _buildSettingItem(
            icon: Icons.description_outlined,
            title: AppLocalizations.of(context)!.termsOfUse,
            onTap: () => _navigateToWebView(
              title: AppLocalizations.of(context)!.termsOfUse,
              url: 'https://example.com/terms-of-use',
            ),
          ),
          const Divider(height: 1),
          _buildVersionItem(),
        ],
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
        child: Row(
          children: [
            Icon(
              icon,
              size: 24,
              color: DesignSpec.primaryItemUnselected,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: DesignSpec.fontSizeBase,
                  fontWeight: DesignSpec.fontWeightMedium,
                ),
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: DesignSpec.primaryItemUnselected,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVersionItem() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 24,
            color: DesignSpec.primaryItemUnselected,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              AppLocalizations.of(context)!.appVersion,
              style: TextStyle(
                fontSize: DesignSpec.fontSizeBase,
                fontWeight: DesignSpec.fontWeightMedium,
              ),
            ),
          ),
          Text(
            _profileService?.getAppVersion() ?? '1.0.0',
            style: TextStyle(
              fontSize: DesignSpec.fontSizeSm,
              color: DesignSpec.primaryItemUnselected,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _navigateToAvatarEdit() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AvatarEditPage(),
      ),
    );

    if (result == true) {
      await _refreshProfile();
    }
  }

  Future<void> _navigateToNicknameEdit() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const NicknameEditPage(),
      ),
    );

    if (result == true) {
      await _refreshProfile();
    }
  }

  Future<void> _navigateToLanguageSettings() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const LanguageSettingsPage(),
      ),
    );

    if (result != null && widget.onLanguageChanged != null) {
      widget.onLanguageChanged!(Locale(result));
      await _profileService?.updateLanguage(result);
      await _refreshProfile();
    }
  }

  Future<void> _navigateToWebView({
    required String title,
    required String url,
  }) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(
          title: title,
          url: url,
        ),
      ),
    );
  }
}
