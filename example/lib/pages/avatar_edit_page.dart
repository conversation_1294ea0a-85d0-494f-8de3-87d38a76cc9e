import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../ui/design_spec.dart';
import '../services/user_profile_service.dart';
import '../models/user_profile.dart';

class AvatarEditPage extends StatefulWidget {
  const AvatarEditPage({super.key});

  @override
  State<AvatarEditPage> createState() => _AvatarEditPageState();
}

class _AvatarEditPageState extends State<AvatarEditPage> {
  UserProfileService? _profileService;
  String? _selectedAvatarPath;
  String? _currentAvatarPath;
  List<String> _maleAvatars = [];
  List<String> _femaleAvatars = [];
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      _profileService = await UserProfileService.getInstance();
      final currentProfile = _profileService!.getCurrentProfile();
      _currentAvatarPath = currentProfile.avatarPath;
      _selectedAvatarPath = currentProfile.avatarPath;
      _maleAvatars = _profileService!.getMaleAvatars();
      _femaleAvatars = _profileService!.getFemaleAvatars();
    } catch (e) {
      debugPrint('初始化头像数据失败: $e');
      _maleAvatars = [];
      _femaleAvatars = [];
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSpec.primaryBackground,
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.editAvatar),
        backgroundColor: DesignSpec.primaryBackground,
        elevation: 0,
        foregroundColor: DesignSpec.primaryText,
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            TextButton(
              onPressed: _hasChanges() ? _saveAvatar : null,
              child: Text(
                AppLocalizations.of(context)!.save,
                style: TextStyle(
                  color: _hasChanges() 
                      ? DesignSpec.primaryItemSelected 
                      : DesignSpec.primaryItemUnselected,
                  fontWeight: DesignSpec.fontWeightMedium,
                ),
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
    );
  }

  Widget _buildContent() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCurrentAvatarSection(),
            const SizedBox(height: 30),
            Expanded(
              child: _buildAvatarCategoriesSection(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentAvatarSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 40,
            backgroundColor: DesignSpec.primaryItemUnselected.withOpacity(0.2),
            child: _selectedAvatarPath?.isNotEmpty == true
                ? ClipOval(
                    child: Image.asset(
                      _selectedAvatarPath!,
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.person,
                          size: 40,
                          color: DesignSpec.primaryItemUnselected,
                        );
                      },
                    ),
                  )
                : Icon(
                    Icons.person,
                    size: 40,
                    color: DesignSpec.primaryItemUnselected,
                  ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context)!.selectAvatar,
                  style: TextStyle(
                    fontSize: DesignSpec.fontSizeLg,
                    fontWeight: DesignSpec.fontWeightMedium,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '选择一个头像来个性化您的个人资料',
                  style: TextStyle(
                    fontSize: DesignSpec.fontSizeSm,
                    color: DesignSpec.primaryItemUnselected,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatarCategoriesSection() {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              color: DesignSpec.secondaryBackground,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TabBar(
              labelColor: DesignSpec.primaryItemSelected,
              unselectedLabelColor: DesignSpec.primaryItemUnselected,
              indicatorColor: DesignSpec.primaryItemSelected,
              indicatorWeight: 3,
              tabs: [
                Tab(
                  icon: Icon(Icons.male),
                  text: AppLocalizations.of(context)!.maleAvatars,
                ),
                Tab(
                  icon: Icon(Icons.female),
                  text: AppLocalizations.of(context)!.femaleAvatars,
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: TabBarView(
              children: [
                _buildAvatarGrid(_maleAvatars),
                _buildAvatarGrid(_femaleAvatars),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyAvatarsPlaceholder() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_library_outlined,
            size: 64,
            color: DesignSpec.primaryItemUnselected.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            '头像资源尚未添加',
            style: TextStyle(
              fontSize: DesignSpec.fontSizeBase,
              color: DesignSpec.primaryItemUnselected,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '请将头像文件添加到 assets/avatars/ 目录',
            style: TextStyle(
              fontSize: DesignSpec.fontSizeSm,
              color: DesignSpec.primaryItemUnselected.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAvatarGrid(List<String> avatars) {
    if (avatars.isEmpty) {
      return _buildEmptyAvatarsPlaceholder();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1,
        ),
        itemCount: avatars.length,
        itemBuilder: (context, index) {
          final avatarPath = avatars[index];
          final isSelected = avatarPath == _selectedAvatarPath;

          return GestureDetector(
            onTap: () => _selectAvatar(avatarPath),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isSelected
                      ? DesignSpec.primaryItemSelected
                      : Colors.transparent,
                  width: 3,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(9),
                child: Image.asset(
                  avatarPath,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: DesignSpec.primaryItemUnselected.withOpacity(0.2),
                      child: Icon(
                        Icons.person,
                        size: 32,
                        color: DesignSpec.primaryItemUnselected,
                      ),
                    );
                  },
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _selectAvatar(String avatarPath) {
    setState(() {
      _selectedAvatarPath = avatarPath;
    });
  }

  bool _hasChanges() {
    return _selectedAvatarPath != _currentAvatarPath;
  }

  Future<void> _saveAvatar() async {
    if (!_hasChanges() || _selectedAvatarPath == null) return;

    setState(() {
      _isSaving = true;
    });

    try {
      await _profileService!.updateAvatarAndGender(_selectedAvatarPath!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.profileSaved),
            backgroundColor: DesignSpec.primaryItemSelected,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.profileSaveFailed),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
