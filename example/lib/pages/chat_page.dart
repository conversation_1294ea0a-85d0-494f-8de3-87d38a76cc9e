import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/chat_session.dart';
import '../models/message.dart';
import '../ui/design_spec.dart';
import '../widgets/message_widget.dart';
import '../network/api.dart';
import '../utils/plot_parser.dart';
import '../services/chat_session_manager.dart';

class ChatPage extends StatefulWidget {
  final ChatSession session;

  const ChatPage({
    super.key,
    required this.session,
  });

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  late ChatSession _session;
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = false;
  bool _hasText = false;
  bool _inputEnabled = true; // 输入框启用状态

  @override
  void initState() {
    super.initState();
    _session = widget.session;
    _textController.addListener(_onTextChanged);
    // 页面加载后滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
    
    // 初始化会话管理器
    ChatSessionManager.instance.initialize();
  }

  void _onTextChanged() {
    final hasText = _textController.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    // 保存当前会话
    if (_session.messages.isNotEmpty) {
      ChatSessionManager.instance.saveSession(_session);
    }
    super.dispose();
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSpec.primaryBackground,
      appBar: AppBar(
        backgroundColor: DesignSpec.secondaryBackground,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          _session.title,
          style: const TextStyle(
            color: Colors.black87,
            fontSize: DesignSpec.fontSizeLg,
            fontWeight: DesignSpec.fontWeightSemiBold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert, color: Colors.black87),
            onPressed: _showMoreOptions,
          ),
        ],
      ),
      body: Column(
        children: [
          // 消息列表
          Expanded(
            child: _session.messages.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    itemCount: _session.messages.length,
                    itemBuilder: (context, index) {
                      return MessageWidget(
                        message: _session.messages[index],
                        onChoiceSelected: _onChoiceSelected,
                      );
                    },
                  ),
          ),
          // 输入区域
          _buildInputArea(),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context)!.startConversation,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputArea() {
    return Container(
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              // 输入框
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: DesignSpec.primaryBackground,
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color: Colors.grey[300]!,
                      width: 1,
                    ),
                  ),
                  child: TextField(
                    controller: _textController,
                    decoration: InputDecoration(
                      hintText: AppLocalizations.of(context)!.inputMessage,
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                    enabled: !_isLoading && _inputEnabled,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // 发送按钮
              Container(
                decoration: BoxDecoration(
                  color: !_hasText || _isLoading || !_inputEnabled
                      ? Colors.grey[300]
                      : DesignSpec.primaryItemSelected,
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  onPressed: !_hasText || _isLoading || !_inputEnabled
                      ? null
                      : _sendMessage,
                  icon: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(
                          Icons.send,
                          color: Colors.white,
                          size: 20,
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _sendMessage() async {
    final text = _textController.text.trim();
    if (text.isEmpty || _isLoading) return;

    // 清空输入框
    _textController.clear();
    setState(() {
      _isLoading = true;
    });

    // 添加用户消息
    final userMessage = Message.textMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: text,
      sender: MessageSender.user,
    );

    setState(() {
      _session = _session.addMessage(userMessage);
    });

    // 保存会话
    await ChatSessionManager.instance.addMessage(_session, userMessage);

    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });

    try {
      // 准备消息列表，使用改进的消息构建逻辑
      final messages = _buildApiMessages(_session.messages);

      // 调用API获取回复
      final response = await Api().chatCompletion(messages, storyId: _session.storyId ?? 'story_1');
      final assistantContent = response.choices?.first.message?.content ?? AppLocalizations.of(context)!.messageSendFailed;
      print("assistantContent: $assistantContent");

      // 使用Plot解析器处理AI响应
      final parseResult = PlotParser.parseContent(assistantContent);
      final baseId = DateTime.now().millisecondsSinceEpoch.toString();
      final messageResult = PlotParser.generateMessagesFromPlot(parseResult, baseId);

      // 添加解析后的消息
      setState(() {
        for (final message in messageResult.messages) {
          _session = _session.addMessage(message);
        }

        // 如果有选择消息，禁用输入框；否则启用
        final hasChoices = messageResult.messages.any((msg) => msg.type == MessageType.choices);
        _inputEnabled = !hasChoices;
      });

      // 如果有图像提示词，异步生成图像
      if (messageResult.hasImagePrompt) {
        _generateImageForMessage(messageResult.imagePrompt!, messageResult.imageMessageId!);
      }

      // 滚动到底部
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    } catch (e) {
      // 增强的错误处理
      String errorContent;
      if (e.toString().contains('重试') && e.toString().contains('次后仍然失败')) {
        errorContent = '${AppLocalizations.of(context)!.messageSendFailed}\n\n'
            '系统已自动重试多次，但LLM输出格式仍然不正确。请稍后再试，或尝试重新表述您的问题。';
      } else {
        errorContent = AppLocalizations.of(context)!.messageSendFailed;
      }

      final errorMessage = Message.plotMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: errorContent,
      );

      setState(() {
        _session = _session.addMessage(errorMessage);
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: DesignSpec.secondaryBackground,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.edit),
                title: Text(AppLocalizations.of(context)!.renameSession),
                onTap: () {
                  Navigator.pop(context);
                  _showRenameDialog();
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: Text(AppLocalizations.of(context)!.deleteSession, style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteConfirmDialog();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showRenameDialog() {
    final controller = TextEditingController(text: _session.title);
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context)!.renameSession),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(
              hintText: '输入新的会话名称',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(AppLocalizations.of(context)!.cancel),
            ),
            TextButton(
              onPressed: () {
                final newTitle = controller.text.trim();
                if (newTitle.isNotEmpty) {
                  setState(() {
                    _session = _session.copyWith(title: newTitle);
                  });
                }
                Navigator.pop(context);
              },
              child: Text(AppLocalizations.of(context)!.confirm),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context)!.deleteSession),
          content: Text(AppLocalizations.of(context)!.deleteConfirmation),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(AppLocalizations.of(context)!.cancel),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context); // 关闭对话框
                Navigator.pop(context); // 返回会话列表
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: Text(AppLocalizations.of(context)!.deleteSession),
            ),
          ],
        );
      },
    );
  }

  /// 处理选项选择
  void _onChoiceSelected(Message message, MessageChoice choice) {
    // 标记选项为已选择
    final updatedChoices = message.choices?.map((c) {
      return c.copyWith(isSelected: c.id == choice.id);
    }).toList();

    final updatedMessage = message.copyWith(
      choices: updatedChoices,
      selectedChoiceId: choice.id,
    );

    // 更新消息
    setState(() {
      final messageIndex = _session.messages.indexWhere((m) => m.id == message.id);
      if (messageIndex != -1) {
        final updatedMessages = List<Message>.from(_session.messages);
        updatedMessages[messageIndex] = updatedMessage;
        _session = _session.copyWith(messages: updatedMessages);
      }
    });

    // 处理不同类型的选择
    if (choice.id == 3) {
      // 第三个选项：启用输入框
      setState(() {
        _inputEnabled = true;
      });
    } else {
      // 前两个选项：发送包含完整选择文本的消息
      _sendChoiceMessage(choice);
    }
  }

  /// 发送选择消息
  void _sendChoiceMessage(MessageChoice choice) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    // 构建包含完整选择内容的消息
    final choiceContent = "我选择：${choice.text}";

    // 添加用户选择消息
    final userMessage = Message.textMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: choiceContent,
      sender: MessageSender.user,
    );

    setState(() {
      _session = _session.addMessage(userMessage);
    });

    // 保存会话
    await ChatSessionManager.instance.addMessage(_session, userMessage);

    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });

    try {
      // 准备消息列表，使用改进的消息构建逻辑
      final messages = _buildApiMessages(_session.messages);

      // 调用API获取回复
      final response = await Api().chatCompletion(messages, storyId: _session.storyId ?? 'story_1');
      final assistantContent = response.choices?.first.message?.content ?? AppLocalizations.of(context)!.messageSendFailed;
      print("assistantContent: $assistantContent");

      // 使用Plot解析器处理AI响应
      final parseResult = PlotParser.parseContent(assistantContent);
      final baseId = DateTime.now().millisecondsSinceEpoch.toString();
      final messageResult = PlotParser.generateMessagesFromPlot(parseResult, baseId);

      // 添加解析后的消息
      setState(() {
        for (final message in messageResult.messages) {
          _session = _session.addMessage(message);
        }

        // 如果有选择消息，禁用输入框；否则启用
        final hasChoices = messageResult.messages.any((msg) => msg.type == MessageType.choices);
        _inputEnabled = !hasChoices;
      });

      // 如果有图像提示词，异步生成图像
      if (messageResult.hasImagePrompt) {
        _generateImageForMessage(messageResult.imagePrompt!, messageResult.imageMessageId!);
      }

      // 滚动到底部
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    } catch (e) {
      // 增强的错误处理
      String errorContent;
      if (e.toString().contains('重试') && e.toString().contains('次后仍然失败')) {
        errorContent = '${AppLocalizations.of(context)!.messageSendFailed}\n\n'
            '系统已自动重试多次，但LLM输出格式仍然不正确。请稍后再试，或尝试重新表述您的问题。';
      } else {
        errorContent = AppLocalizations.of(context)!.messageSendFailed;
      }

      final errorMessage = Message.plotMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: errorContent,
      );

      setState(() {
        _session = _session.addMessage(errorMessage);
        _inputEnabled = true; // 错误时重新启用输入框
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 为指定消息生成图像
  void _generateImageForMessage(String imagePrompt, String messageId) async {
    try {
      final imageUrl = await PlotParser.generateImage(imagePrompt);

      if (imageUrl != null && imageUrl.isNotEmpty) {
        // 找到对应的消息并更新
        final messageIndex = _session.messages.indexWhere((msg) => msg.id == messageId);
        if (messageIndex != -1) {
          final originalMessage = _session.messages[messageIndex];
          final updatedMessage = originalMessage.copyWith(
            imageUrl: imageUrl,
            content: '', // 清空加载提示文本
          );

          setState(() {
            final updatedMessages = List<Message>.from(_session.messages);
            updatedMessages[messageIndex] = updatedMessage;
            _session = _session.copyWith(messages: updatedMessages);
          });
        }
      } else {
        // 图像生成失败，更新消息内容
        final messageIndex = _session.messages.indexWhere((msg) => msg.id == messageId);
        if (messageIndex != -1) {
          final originalMessage = _session.messages[messageIndex];
          final updatedMessage = originalMessage.copyWith(
            content: '图像生成失败',
          );

          setState(() {
            final updatedMessages = List<Message>.from(_session.messages);
            updatedMessages[messageIndex] = updatedMessage;
            _session = _session.copyWith(messages: updatedMessages);
          });
        }
      }
    } catch (e) {
      // 处理异常情况
      final messageIndex = _session.messages.indexWhere((msg) => msg.id == messageId);
      if (messageIndex != -1) {
        final originalMessage = _session.messages[messageIndex];
        final updatedMessage = originalMessage.copyWith(
          content: '图像生成失败: ${e.toString()}',
        );

        setState(() {
          final updatedMessages = List<Message>.from(_session.messages);
          updatedMessages[messageIndex] = updatedMessage;
          _session = _session.copyWith(messages: updatedMessages);
        });
      }
    }
  }

  /// 构建传递给API的消息列表，优化上下文信息
  List<Map<String, String>> _buildApiMessages(List<Message> messages) {
    final apiMessages = <Map<String, String>>[];

    for (final message in messages) {
      String role;
      String content = message.content;

      // 确定消息角色
      if (message.sender == MessageSender.user) {
        role = 'user';
      } else if (message.sender == MessageSender.assistant) {
        role = 'assistant';
      } else {
        role = 'system';
      }

      // 对于选择类型的消息，添加更多上下文信息
      if (message.type == MessageType.choices && message.choices != null) {
        // 为选择消息添加选项信息到内容中
        final choicesText = message.choices!.map((choice) =>
          "${choice.id}. ${choice.text}").join("\n");
        content = "$content\n可选择的行动：\n$choicesText";

        // 如果有已选择的选项，添加选择信息
        if (message.selectedChoiceId != null) {
          final selectedChoice = message.choices!.firstWhere(
            (choice) => choice.id == message.selectedChoiceId,
            orElse: () => MessageChoice(id: 0, text: "未知选择"),
          );
          content += "\n[用户选择了：${selectedChoice.text}]";
        }
      }

      // 对于剧情描述消息，确保包含完整信息
      if (message.type == MessageType.plot) {
        content = "[剧情描述] $content";
      }

      // 对于图像消息，添加场景描述
      if (message.type == MessageType.image && message.imageUrl != null) {
        content = "[场景图像] $content";
      }

      apiMessages.add({
        'role': role,
        'content': content,
      });
    }

    return apiMessages;
  }
}