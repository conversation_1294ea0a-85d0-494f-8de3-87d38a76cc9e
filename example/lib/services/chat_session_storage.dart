import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/chat_session.dart';
import '../models/message.dart';

class ChatSessionStorage {
  static const String _sessionIndexKey = 'chat_sessions_index';
  static const String _sessionKeyPrefix = 'chat_session_';
  static const int _maxSessions = 50;
  static const int _maxSessionAgeDays = 30;

  final SharedPreferences _prefs;
  final Uuid _uuid = const Uuid();

  ChatSessionStorage(this._prefs);

  static Future<ChatSessionStorage> create() async {
    final prefs = await SharedPreferences.getInstance();
    return ChatSessionStorage(prefs);
  }

  Future<void> saveSession(ChatSession session) async {
    try {
      final sessionJson = session.toJson();
      final sessionData = jsonEncode(sessionJson);
      
      if (sessionData.length > 100 * 1024) {
        throw Exception('会话数据超过100KB限制');
      }

      final sessionKey = '$_sessionKeyPrefix${session.id}';
      await _prefs.setString(sessionKey, sessionData);
      
      await _updateSessionIndex(session.id);
      await cleanupOldSessions();
    } catch (e) {
      throw Exception('保存会话失败: $e');
    }
  }

  Future<ChatSession?> loadSession(String sessionId) async {
    try {
      final sessionKey = '$_sessionKeyPrefix$sessionId';
      final sessionData = _prefs.getString(sessionKey);
      
      if (sessionData == null) return null;
      
      final sessionJson = jsonDecode(sessionData);
      return ChatSession.fromJson(sessionJson);
    } catch (e) {
      return null;
    }
  }

  Future<List<ChatSession>> getAllSessions() async {
    try {
      final sessionIds = _getSessionIndex();
      final sessions = <ChatSession>[];
      
      for (final sessionId in sessionIds) {
        final session = await loadSession(sessionId);
        if (session != null) {
          sessions.add(session);
        }
      }
      
      sessions.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
      return sessions;
    } catch (e) {
      return [];
    }
  }

  Future<void> deleteSession(String sessionId) async {
    try {
      final sessionKey = '$_sessionKeyPrefix$sessionId';
      await _prefs.remove(sessionKey);
      
      final sessionIds = _getSessionIndex();
      sessionIds.remove(sessionId);
      await _prefs.setStringList(_sessionIndexKey, sessionIds);
    } catch (e) {
      throw Exception('删除会话失败: $e');
    }
  }

  Future<void> cleanupOldSessions() async {
    try {
      final sessionIds = _getSessionIndex();
      final now = DateTime.now();
      final sessionsToRemove = <String>[];
      
      for (final sessionId in sessionIds) {
        final session = await loadSession(sessionId);
        if (session != null) {
          final age = now.difference(session.updatedAt).inDays;
          if (age > _maxSessionAgeDays) {
            sessionsToRemove.add(sessionId);
          }
        } else {
          sessionsToRemove.add(sessionId);
        }
      }
      
      for (final sessionId in sessionsToRemove) {
        await deleteSession(sessionId);
      }
      
      if (sessionIds.length - sessionsToRemove.length > _maxSessions) {
        final remainingSessions = await getAllSessions();
        if (remainingSessions.length > _maxSessions) {
          final sessionsToDelete = remainingSessions.sublist(_maxSessions);
          for (final session in sessionsToDelete) {
            await deleteSession(session.id);
          }
        }
      }
    } catch (e) {
      return;
    }
  }

  List<String> _getSessionIndex() {
    return _prefs.getStringList(_sessionIndexKey) ?? [];
  }

  Future<void> _updateSessionIndex(String sessionId) async {
    final sessionIds = _getSessionIndex();
    if (!sessionIds.contains(sessionId)) {
      sessionIds.insert(0, sessionId);
      await _prefs.setStringList(_sessionIndexKey, sessionIds);
    }
  }

  Future<void> clearAllSessions() async {
    try {
      final sessionIds = _getSessionIndex();
      for (final sessionId in sessionIds) {
        final sessionKey = '$_sessionKeyPrefix$sessionId';
        await _prefs.remove(sessionKey);
      }
      await _prefs.remove(_sessionIndexKey);
    } catch (e) {
      throw Exception('清空会话失败: $e');
    }
  }

  Future<int> getSessionCount() async {
    return _getSessionIndex().length;
  }

  Future<bool> hasSession(String sessionId) async {
    final sessionKey = '$_sessionKeyPrefix$sessionId';
    return _prefs.containsKey(sessionKey);
  }
}