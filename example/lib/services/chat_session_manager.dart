import 'dart:async';
import 'dart:convert';
import '../models/chat_session.dart';
import '../models/message.dart';
import 'chat_session_storage.dart';

class ChatSessionManager {
  static final ChatSessionManager _instance = ChatSessionManager._internal();
  static ChatSessionManager get instance => _instance;

  ChatSessionManager._internal();

  ChatSessionStorage? _storage;
  Timer? _saveTimer;
  ChatSession? _currentSession;
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;
    _storage = await ChatSessionStorage.create();
    _isInitialized = true;
  }

  Future<ChatSession> createSession({
    required String storyId,
    required String storyTitle,
    String? avatarUrl,
  }) async {
    await initialize();
    
    final session = ChatSession(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: storyTitle,
      avatarUrl: avatarUrl,
      messages: [],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      storyId: storyId,
    );

    _currentSession = session;
    return session;
  }

  Future<void> saveSession(ChatSession session) async {
    await initialize();
    await _storage!.saveSession(session);
  }

  Future<void> addMessage(ChatSession session, Message message) async {
    await initialize();
    
    final updatedSession = session.addMessage(message);
    _currentSession = updatedSession;
    
    if (session.messages.isEmpty) {
      await _saveImmediately(updatedSession);
    } else {
      _scheduleSave(updatedSession);
    }
  }

  Future<List<ChatSession>> getAllSessions() async {
    await initialize();
    return await _storage!.getAllSessions();
  }

  Future<ChatSession?> loadSession(String sessionId) async {
    await initialize();
    return await _storage!.loadSession(sessionId);
  }

  Future<void> deleteSession(String sessionId) async {
    await initialize();
    await _storage!.deleteSession(sessionId);
    
    if (_currentSession?.id == sessionId) {
      _currentSession = null;
    }
  }

  Future<void> cleanupOldSessions() async {
    await initialize();
    await _storage!.cleanupOldSessions();
  }

  ChatSession? get currentSession => _currentSession;

  Future<void> _saveImmediately(ChatSession session) async {
    try {
      await _storage?.saveSession(session);
    } catch (e) {
      print('立即保存会话失败: $e');
    }
  }

  void _scheduleSave(ChatSession session) {
    _saveTimer?.cancel();
    _saveTimer = Timer(const Duration(milliseconds: 500), () async {
      try {
        await _storage?.saveSession(session);
      } catch (e) {
        print('延迟保存会话失败: $e');
      }
    });
  }

  void dispose() {
    _saveTimer?.cancel();
    if (_currentSession != null) {
      _saveImmediately(_currentSession!);
    }
  }

  Future<void> clearAllSessions() async {
    await initialize();
    await _storage!.clearAllSessions();
    _currentSession = null;
  }

  Future<bool> hasActiveSessions() async {
    await initialize();
    final sessions = await _storage!.getAllSessions();
    return sessions.isNotEmpty;
  }

  Future<ChatSession?> getLatestSessionForStory(String storyId) async {
    await initialize();
    final sessions = await _storage!.getAllSessions();
    
    for (final session in sessions) {
      if (session.storyId == storyId) {
        return session;
      }
    }
    
    return null;
  }
}