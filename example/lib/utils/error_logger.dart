import 'dart:developer' as developer;

/// 简化的错误日志记录器（仅控制台输出）
class ErrorLogger {
  /// 错误统计
  static final Map<String, int> _errorStats = {};
  
  /// 记录JSON解析错误
  static void logJsonParseError({
    required String errorType,
    required String content,
    required String error,
    int? attemptNumber,
  }) {
    // 更新统计
    _errorStats[errorType] = (_errorStats[errorType] ?? 0) + 1;

    // 记录到开发者日志
    developer.log(
      'JSON解析错误: $errorType (尝试: $attemptNumber)',
      name: 'ErrorLogger',
      error: 'Error: $error, Content preview: ${content.length > 100 ? content.substring(0, 100) + '...' : content}',
    );
  }

  /// 记录重试成功事件
  static void logRetrySuccess({
    required int totalAttempts,
    required List<String> attemptErrors,
  }) {
    developer.log(
      '重试成功，总尝试次数: $totalAttempts',
      name: 'ErrorLogger',
    );
  }

  /// 记录重试失败事件
  static void logRetryFailure({
    required int totalAttempts,
    required List<String> attemptErrors,
    required String finalError,
  }) {
    developer.log(
      '重试失败，总尝试次数: $totalAttempts',
      name: 'ErrorLogger',
      error: finalError,
    );
  }

  /// 获取错误统计
  static Map<String, int> getErrorStats() {
    return Map.from(_errorStats);
  }

  /// 清除错误统计
  static void clearErrorStats() {
    _errorStats.clear();
  }

  /// 生成简单的错误报告
  static String generateErrorReport() {
    final stats = getErrorStats();

    final report = StringBuffer();
    report.writeln('=== LLM错误统计 ===');
    report.writeln('生成时间: ${DateTime.now().toIso8601String()}');
    report.writeln();

    if (stats.isEmpty) {
      report.writeln('暂无错误统计数据');
    } else {
      stats.forEach((errorType, count) {
        report.writeln('$errorType: $count 次');
      });
    }

    return report.toString();
  }
}
