/// 示例Plot响应数据，用于测试Plot解析功能
class SamplePlotResponses {
  /// 完整的Plot JSON响应
  static const String fullPlotResponse = '''
{
  "plot_description": {
    "content": "岩石守卫发出一声沉闷的轰鸣，重重地砸向地面，震落了天花板上的碎石。你注意到它胸口的核心符文在攻击后会短暂暗淡，似乎是它的弱点。但同时，祭坛周围的四个小石像也开始活化。"
  },
  "dialogue": {
    "character": "艾拉",
    "status": "举起法杖维持着一个防护罩",
    "content": "它的能量核心暴露了！但我需要时间准备一个破解法术。"
  },
  "choices": {
    "options": [
      {"id": 1, "text": "冲上前去，正面吸引岩石守卫的注意，为艾拉争取时间"},
      {"id": 2, "text": "优先击碎正在活化的小石像，防止被围攻"},
      {"id": 3, "text": "(自由对话)"}
    ]
  },
  "image_prompt": "古代地下神庙, 石制祭坛, 魔法符文, 昏暗光线, 神秘氛围"
}
''';

  /// 只有剧情描述的响应
  static const String plotOnlyResponse = '''
{
  "plot_description": {
    "content": "你走进了一个古老的图书馆，书架上摆满了厚重的魔法书籍。空气中弥漫着羊皮纸和墨水的味道，偶尔有微弱的魔法光芒从某些书籍中透出。"
  }
}
''';

  /// 只有对话的响应
  static const String dialogueOnlyResponse = '''
{
  "dialogue": {
    "character": "图书管理员",
    "status": "从书架后面走出来",
    "content": "欢迎来到古老的智慧殿堂，年轻的冒险者。你在寻找什么特别的知识吗？"
  }
}
''';

  /// 只有选择的响应
  static const String choicesOnlyResponse = '''
{
  "choices": {
    "options": [
      {"id": 1, "text": "询问关于龙族历史的书籍"},
      {"id": 2, "text": "寻找魔法咒语的古籍"},
      {"id": 3, "text": "(自由对话)"}
    ]
  }
}
''';

  /// 混合格式响应（JSON + 额外文本）
  static const String mixedFormatResponse = '''
战斗进入了白热化阶段，你感到体力正在快速消耗。

{
  "plot_description": {
    "content": "敌人的攻击越来越猛烈，但你也发现了它的攻击模式中的破绽。"
  },
  "choices": {
    "options": [
      {"id": 1, "text": "利用破绽发动反击"},
      {"id": 2, "text": "暂时撤退，寻找更好的时机"},
      {"id": 3, "text": "(自由对话)"}
    ]
  }
}

这是一个关键的决定时刻！
''';

  /// 纯文本响应
  static const String textOnlyResponse = '''
你的选择很明智。经过一番激烈的战斗，你成功击败了敌人。现在你可以继续前进，探索这个神秘的地下城了。

获得经验值：150
获得金币：75
获得物品：魔法水晶 x1
''';

  /// 无效的JSON响应
  static const String invalidJsonResponse = '''
{
  "plot_description": {
    "content": "这是一个格式错误的JSON
  }
  // 缺少闭合括号
''';

  /// 空响应
  static const String emptyResponse = '';

  /// 图像生成测试响应
  static const String imageGenerationResponse = '''
{
  "plot_description": {
    "content": "你走进了一座废弃的赛博朋克城市，霓虹灯在雨夜中闪烁，高楼大厦的玻璃幕墙反射着五彩斑斓的光芒。街道上弥漫着蒸汽，远处传来机械的嗡鸣声。"
  },
  "dialogue": {
    "character": "瑞雯",
    "status": "调整着眼部的增强现实显示器",
    "content": "这里就是传说中的数据港区，小心点，这里到处都是陷阱。"
  },
  "choices": {
    "options": [
      {"id": 1, "text": "沿着主街道前进，寻找信息贩子"},
      {"id": 2, "text": "从小巷穿行，避开监控摄像头"},
      {"id": 3, "text": "(自由对话)"}
    ]
  },
  "image_prompt": "赛博朋克城市, 雨夜, 霓虹灯, 高楼大厦, 玻璃幕墙, 蒸汽, 未来科技"
}
''';

  /// 获取所有示例响应
  static List<String> getAllSamples() {
    return [
      fullPlotResponse,
      plotOnlyResponse,
      dialogueOnlyResponse,
      choicesOnlyResponse,
      mixedFormatResponse,
      textOnlyResponse,
      invalidJsonResponse,
      emptyResponse,
      imageGenerationResponse,
    ];
  }

  /// 获取示例响应的描述
  static List<String> getSampleDescriptions() {
    return [
      '完整Plot响应（包含剧情、对话、选择、图像）',
      '仅剧情描述响应',
      '仅对话响应',
      '仅选择响应',
      '混合格式响应（JSON + 文本）',
      '纯文本响应',
      '无效JSON响应',
      '空响应',
      '图像生成测试响应（包含图像提示词）',
    ];
  }
}
