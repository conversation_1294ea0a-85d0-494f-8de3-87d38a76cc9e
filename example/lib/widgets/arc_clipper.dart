import 'package:flutter/material.dart';

/// 自定义剪裁路径，实现顶部和底部边缘为弧线的效果
/// 支持正数（凸起弧线，高度减少）和负数（凹陷弧线，边界内）的arcHeight
/// 根据arcHeight正负值自动选择不同的实现方式
class ArcClipper extends CustomClipper<Path> {
  final double borderRadius;
  final double arcHeight;

  const ArcClipper({
    this.borderRadius = 12.0,
    this.arcHeight = 8.0,
  });

  @override
  Path getClip(Size size) {
    final path = Path();
    final width = size.width;
    final height = size.height;

    // 限制圆角大小
    final maxRadius = (width / 2).clamp(0.0, height / 2);
    final actualRadius = borderRadius.clamp(0.0, maxRadius);

    if (arcHeight >= 0) {
      // 正数：凸起弧线，子控件高度减少
      return _buildConvexArc(size, width, height, actualRadius);
    } else {
      // 负数：凹陷弧线，在边界内实现
      return _buildConcaveArc(size, width, height, actualRadius);
    }
  }

  // 构建凸起弧线（正数arcHeight）
  Path _buildConvexArc(Size size, double width, double height, double actualRadius) {
    final path = Path();
    
    // 限制弧线高度，确保不会超过控件高度的一半
    final maxArcHeight = (height / 2 - actualRadius).clamp(0.0, height / 2);
    final effectiveArcHeight = arcHeight.clamp(0.0, maxArcHeight);

    // 计算实际的弧线控制点
    final topArcControlY = effectiveArcHeight;
    final bottomArcControlY = height - effectiveArcHeight;

    // 从左上角开始，考虑圆角
    path.moveTo(0, actualRadius + effectiveArcHeight);

    // 左上角圆角
    path.quadraticBezierTo(
      0,
      effectiveArcHeight,
      actualRadius,
      effectiveArcHeight
    );

    // 顶部弧线（向内凸起）
    final topStartX = actualRadius;
    final topEndX = width - actualRadius;
    
    path.quadraticBezierTo(
      width / 2,              // 控制点X：中点
      0,                      // 控制点Y：顶部边缘
      topEndX,                // 结束点X
      effectiveArcHeight      // 结束点Y
    );

    // 右上角圆角
    path.quadraticBezierTo(
      width,
      effectiveArcHeight,
      width,
      actualRadius + effectiveArcHeight
    );

    // 右边
    path.lineTo(width, bottomArcControlY - actualRadius);

    // 右下角圆角
    path.quadraticBezierTo(
      width,
      bottomArcControlY,
      width - actualRadius,
      bottomArcControlY
    );

    // 底部弧线（向内凸起）
    final bottomStartX = width - actualRadius;
    final bottomEndX = actualRadius;
    
    path.quadraticBezierTo(
      width / 2,              // 控制点X：中点
      height,                 // 控制点Y：底部边缘
      bottomEndX,             // 结束点X
      bottomArcControlY       // 结束点Y
    );

    // 左下角圆角
    path.quadraticBezierTo(
      0,
      bottomArcControlY,
      0,
      bottomArcControlY - actualRadius
    );

    // 左边
    path.lineTo(0, actualRadius + effectiveArcHeight);

    path.close();
    return path;
  }

  // 构建凹陷弧线（负数arcHeight）
  Path _buildConcaveArc(Size size, double width, double height, double actualRadius) {
    final path = Path();
    
    // 计算弧线高度的绝对值
    final absArcHeight = arcHeight.abs();
    
    // 限制弧线高度，确保弧线完全在控件边界内
    final maxArcHeight = (height / 2 - actualRadius).clamp(0.0, height / 2);
    final effectiveArcHeight = absArcHeight.clamp(0.0, maxArcHeight);

    // 计算弧线控制点的Y坐标（凹陷弧线，控制点在边界内）
    final topControlY = effectiveArcHeight;
    final bottomControlY = height - effectiveArcHeight;

    // 从左上角开始，考虑圆角
    path.moveTo(0, actualRadius);

    // 左上角圆角
    path.quadraticBezierTo(
      0,
      0,
      actualRadius,
      0
    );

    // 顶部弧线（向内凹陷）
    final topEndX = width - actualRadius;
    
    path.quadraticBezierTo(
      width / 2,              // 控制点X：中点
      topControlY,            // 控制点Y：边界内
      topEndX,                // 结束点X
      0                       // 结束点Y：顶部边缘
    );

    // 右上角圆角
    path.quadraticBezierTo(
      width,
      0,
      width,
      actualRadius
    );

    // 右边
    path.lineTo(width, height - actualRadius);

    // 右下角圆角
    path.quadraticBezierTo(
      width,
      height,
      width - actualRadius,
      height
    );

    // 底部弧线（向内凹陷）
    final bottomEndX = actualRadius;
    
    path.quadraticBezierTo(
      width / 2,              // 控制点X：中点
      bottomControlY,         // 控制点Y：边界内
      bottomEndX,             // 结束点X
      height                  // 结束点Y：底部边缘
    );

    // 左下角圆角
    path.quadraticBezierTo(
      0,
      height,
      0,
      height - actualRadius
    );

    // 左边
    path.lineTo(0, actualRadius);

    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant ArcClipper oldClipper) {
    return oldClipper.borderRadius != borderRadius ||
        oldClipper.arcHeight != arcHeight;
  }
}

/// 类似ClipRRect的Widget，但具有顶部和底部弧线效果
/// 支持正数（凸起弧线，子控件高度减少 arcHeight * 2）
/// 和负数（凹陷弧线，在子控件边界内实现）的arcHeight
/// 根据arcHeight正负值自动选择不同的实现方式
class ClipArcRRect extends StatelessWidget {
  final Widget child;
  final double borderRadius;
  final double arcHeight;
  final Clip clipBehavior;

  const ClipArcRRect({
    super.key,
    required this.child,
    this.borderRadius = 12.0,
    this.arcHeight = 8.0,
    this.clipBehavior = Clip.antiAlias,
  });

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipBehavior: clipBehavior,
      clipper: ArcClipper(
        borderRadius: borderRadius,
        arcHeight: arcHeight,
      ),
      child: child,
    );
  }
}