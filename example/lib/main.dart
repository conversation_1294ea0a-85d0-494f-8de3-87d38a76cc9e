import 'package:chat_flutter_example/ui/design_spec.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'pages/chat_session_list_page.dart';
import 'pages/storyboard_page.dart';
import 'pages/profile_page.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with SingleTickerProviderStateMixin {
  TabController? tabController;
  Locale _locale = const Locale('en');

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: 3, vsync: this);
  }

  void _changeLanguage(Locale newLocale) {
    setState(() {
      _locale = newLocale;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      color: DesignSpec.primaryBackground,
      locale: _locale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en'), // English
        Locale('zh'), // Traditional Chinese
        Locale('ar'), // Arabic
        Locale('hi'), // Hindi
      ],
      home: DefaultTabController(
        length: 3,
        child: Scaffold(
          backgroundColor: DesignSpec.primaryBackground,
          body: Stack(
            children: [
              Positioned.fill(
                child: TabBarView(
                  controller: tabController,
                  children: [
                    StoryBoardPage(),
                    ChatSessionListPage(),
                    ProfilePage(onLanguageChanged: _changeLanguage),
                  ],
                ),
              ),
              Positioned(
                  bottom: MediaQuery.of(context).padding.bottom + 15,
                  left: 20,
                  right: 20,
                  child: Container(
                    height: 70,
                    decoration: BoxDecoration(
                      color: DesignSpec.secondaryBackground,
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: Offset(0, 5),
                        ),
                      ],
                    ),
                  )
              ),
              Positioned(
                bottom: 20,
                left: 20,
                right: 20,
                child: Builder(
                  builder: (context) {
                    return BottomNavigationBar(
                      backgroundColor: Colors.transparent,
                      elevation: 0,
                      currentIndex: tabController?.index ?? 0,
                      onTap: (index) {
                        tabController?.animateTo(index);
                        setState(() {});
                      },
                      selectedItemColor: DesignSpec.primaryItemSelected,
                      unselectedItemColor: DesignSpec.primaryItemUnselected,
                      items: [
                        BottomNavigationBarItem(
                            icon: const Icon(Icons.menu_book), label: AppLocalizations.of(context)!.storyBoard),
                        BottomNavigationBarItem(
                            icon: const Icon(Icons.chat), label: AppLocalizations.of(context)!.chat),
                        BottomNavigationBarItem(
                            icon: const Icon(Icons.person), label: AppLocalizations.of(context)!.profile),
                      ],
                    );
                  }
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
