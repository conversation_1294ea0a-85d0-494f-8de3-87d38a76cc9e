enum Gender {
  male,
  female,
  unspecified;

  String get displayName {
    switch (this) {
      case Gender.male:
        return 'Male';
      case Gender.female:
        return 'Female';
      case Gender.unspecified:
        return 'Unspecified';
    }
  }

  static Gender fromString(String value) {
    switch (value.toLowerCase()) {
      case 'male':
        return Gender.male;
      case 'female':
        return Gender.female;
      default:
        return Gender.unspecified;
    }
  }
}

class UserProfile {
  final String id;
  final String nickname;
  final String avatarPath;
  final String language;
  final Gender gender;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserProfile({
    required this.id,
    required this.nickname,
    required this.avatarPath,
    required this.language,
    required this.gender,
    required this.createdAt,
    required this.updatedAt,
  });

  // 默认用户配置
  factory UserProfile.defaultProfile() {
    final now = DateTime.now();
    return UserProfile(
      id: 'default_user',
      nickname: 'User',
      avatarPath: '', // 使用空字符串表示未设置头像
      language: 'en',
      gender: Gender.unspecified,
      createdAt: now,
      updatedAt: now,
    );
  }

  UserProfile copyWith({
    String? id,
    String? nickname,
    String? avatarPath,
    String? language,
    Gender? gender,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfile(
      id: id ?? this.id,
      nickname: nickname ?? this.nickname,
      avatarPath: avatarPath ?? this.avatarPath,
      language: language ?? this.language,
      gender: gender ?? this.gender,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nickname': nickname,
      'avatarPath': avatarPath,
      'language': language,
      'gender': gender.name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'] as String,
      nickname: json['nickname'] as String,
      avatarPath: json['avatarPath'] as String,
      language: json['language'] as String,
      gender: Gender.fromString(json['gender'] as String? ?? 'unspecified'),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserProfile && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserProfile(id: $id, nickname: $nickname, avatarPath: $avatarPath, language: $language, gender: $gender)';
  }
}
