enum MessageType {
  plot,      // 剧情提示消息
  image,     // 插画消息
  text,      // 普通文字消息
  system,      // 系统消息
  choices,   // 选择分支消息
}

enum MessageSender {
  user,      // 用户发送
  assistant, // AI助手发送
  system,    // 系统发送
}

class MessageChoice {
  final int id;
  final String text;
  final bool isSelected;

  MessageChoice({
    required this.id,
    required this.text,
    this.isSelected = false,
  });

  MessageChoice copyWith({
    int? id,
    String? text,
    bool? isSelected,
  }) {
    return MessageChoice(
      id: id ?? this.id,
      text: text ?? this.text,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'isSelected': isSelected,
    };
  }

  factory MessageChoice.fromJson(Map<String, dynamic> json) {
    return MessageChoice(
      id: json['id'] as int,
      text: json['text'] as String,
      isSelected: json['isSelected'] as bool? ?? false,
    );
  }
}

class Message {
  final String id;
  final MessageType type;
  final MessageSender sender;
  final String content;
  final String? imageUrl;
  final DateTime timestamp;
  final bool isRead;
  final List<MessageChoice>? choices;
  final int? selectedChoiceId;

  Message({
    required this.id,
    required this.type,
    required this.sender,
    required this.content,
    this.imageUrl,
    required this.timestamp,
    this.isRead = false,
    this.choices,
    this.selectedChoiceId,
  });

  Message copyWith({
    String? id,
    MessageType? type,
    MessageSender? sender,
    String? content,
    String? imageUrl,
    DateTime? timestamp,
    bool? isRead,
    List<MessageChoice>? choices,
    int? selectedChoiceId,
  }) {
    return Message(
      id: id ?? this.id,
      type: type ?? this.type,
      sender: sender ?? this.sender,
      content: content ?? this.content,
      imageUrl: imageUrl ?? this.imageUrl,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      choices: choices ?? this.choices,
      selectedChoiceId: selectedChoiceId ?? this.selectedChoiceId,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'sender': sender.name,
      'content': content,
      'imageUrl': imageUrl,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
      'choices': choices?.map((choice) => choice.toJson()).toList(),
      'selectedChoiceId': selectedChoiceId,
    };
  }

  factory Message.fromJson(Map<String, dynamic> json) {
    List<MessageChoice>? choices;
    if (json['choices'] != null) {
      choices = (json['choices'] as List)
          .map((choiceJson) => MessageChoice.fromJson(choiceJson))
          .toList();
    }

    return Message(
      id: json['id'] as String,
      type: MessageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MessageType.text,
      ),
      sender: MessageSender.values.firstWhere(
        (e) => e.name == json['sender'],
        orElse: () => MessageSender.user,
      ),
      content: json['content'] as String,
      imageUrl: json['imageUrl'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isRead: json['isRead'] as bool? ?? false,
      choices: choices,
      selectedChoiceId: json['selectedChoiceId'] as int?,
    );
  }

  // 便捷构造函数
  factory Message.plotMessage({
    required String id,
    required String content,
    DateTime? timestamp,
  }) {
    return Message(
      id: id,
      type: MessageType.plot,
      sender: MessageSender.system,
      content: content,
      timestamp: timestamp ?? DateTime.now(),
      isRead: true,
    );
  }

  factory Message.imageMessage({
    required String id,
    required String imageUrl,
    String content = '',
    MessageSender sender = MessageSender.assistant,
    DateTime? timestamp,
  }) {
    return Message(
      id: id,
      type: MessageType.image,
      sender: sender,
      content: content,
      imageUrl: imageUrl,
      timestamp: timestamp ?? DateTime.now(),
    );
  }

  factory Message.textMessage({
    required String id,
    required String content,
    required MessageSender sender,
    DateTime? timestamp,
  }) {
    return Message(
      id: id,
      type: MessageType.text,
      sender: sender,
      content: content,
      timestamp: timestamp ?? DateTime.now(),
    );
  }

  factory Message.choicesMessage({
    required String id,
    required String content,
    required List<MessageChoice> choices,
    MessageSender sender = MessageSender.assistant,
    DateTime? timestamp,
  }) {
    return Message(
      id: id,
      type: MessageType.choices,
      sender: sender,
      content: content,
      choices: choices,
      timestamp: timestamp ?? DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Message && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Message(id: $id, type: $type, sender: $sender, content: $content, timestamp: $timestamp)';
  }
}