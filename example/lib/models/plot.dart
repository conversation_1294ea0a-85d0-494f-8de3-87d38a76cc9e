/// plot_description : {"content":"岩石守卫发出一声沉闷的轰鸣，重重地砸向地面，震落了天花板上的碎石。你注意到它胸口的核心符文在攻击后会短暂暗淡，似乎是它的弱点。但同时，祭坛周围的四个小石像也开始活化。"}
/// dialogue : {"character":"艾拉","status":"举起法杖维持着一个防护罩","content":"它的能量核心暴露了！但我需要时间准备一个破解法术。"}
/// choices : {"options":[{"id":1,"text":"冲上前去，正面吸引岩石守卫的注意，为艾拉争取时间"},{"id":2,"text":"优先击碎正在活化的小石像，防止被围攻"},{"id":3,"text":"(自由对话)"}]}
/// image_prompt : "古代宫殿, 夜晚, 月光, 庭院, 石阶"
class Plot {
  Plot({
    this.plotDescription,
    this.dialogue,
    this.choices,
    this.imagePrompt,
  });

  Plot.fromJson(dynamic json) {
    plotDescription = json['plot_description'] != null
        ? PlotDescription.fromJson(json['plot_description'])
        : null;
    dialogue =
        json['dialogue'] != null ? Dialogue.fromJson(json['dialogue']) : null;
    choices =
        json['choices'] != null ? Choices.fromJson(json['choices']) : null;
    imagePrompt = json['image_prompt'];
  }

  PlotDescription? plotDescription;
  Dialogue? dialogue;
  Choices? choices;
  String? imagePrompt;

  Plot copyWith({
    PlotDescription? plotDescription,
    Dialogue? dialogue,
    Choices? choices,
    String? imagePrompt,
  }) =>
      Plot(
        plotDescription: plotDescription ?? this.plotDescription,
        dialogue: dialogue ?? this.dialogue,
        choices: choices ?? this.choices,
        imagePrompt: imagePrompt ?? this.imagePrompt,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (plotDescription != null) {
      map['plot_description'] = plotDescription?.toJson();
    }
    if (dialogue != null) {
      map['dialogue'] = dialogue?.toJson();
    }
    if (choices != null) {
      map['choices'] = choices?.toJson();
    }
    if (imagePrompt != null) {
      map['image_prompt'] = imagePrompt;
    }
    return map;
  }
}

/// options : [{"id":1,"text":"冲上前去，正面吸引岩石守卫的注意，为艾拉争取时间"},{"id":2,"text":"优先击碎正在活化的小石像，防止被围攻"},{"id":3,"text":"(自由对话)"}]

class Choices {
  Choices({
    this.options,
  });

  Choices.fromJson(dynamic json) {
    if (json['options'] != null) {
      options = [];
      json['options'].forEach((v) {
        options?.add(Options.fromJson(v));
      });
    }
  }

  List<Options>? options;

  Choices copyWith({
    List<Options>? options,
  }) =>
      Choices(
        options: options ?? this.options,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (options != null) {
      map['options'] = options?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

/// id : 1
/// text : "冲上前去，正面吸引岩石守卫的注意，为艾拉争取时间"

class Options {
  Options({
    this.id,
    this.text,
  });

  Options.fromJson(dynamic json) {
    id = json['id'];
    text = json['text'];
  }

  num? id;
  String? text;

  Options copyWith({
    num? id,
    String? text,
  }) =>
      Options(
        id: id ?? this.id,
        text: text ?? this.text,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['text'] = text;
    return map;
  }
}

/// character : "艾拉"
/// status : "举起法杖维持着一个防护罩"
/// content : "它的能量核心暴露了！但我需要时间准备一个破解法术。"

class Dialogue {
  Dialogue({
    this.character,
    this.status,
    this.content,
  });

  Dialogue.fromJson(dynamic json) {
    character = json['character'];
    status = json['status'];
    content = json['content'];
  }

  String? character;
  String? status;
  String? content;

  Dialogue copyWith({
    String? character,
    String? status,
    String? content,
  }) =>
      Dialogue(
        character: character ?? this.character,
        status: status ?? this.status,
        content: content ?? this.content,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['character'] = character;
    map['status'] = status;
    map['content'] = content;
    return map;
  }
}

/// content : "岩石守卫发出一声沉闷的轰鸣，重重地砸向地面，震落了天花板上的碎石。你注意到它胸口的核心符文在攻击后会短暂暗淡，似乎是它的弱点。但同时，祭坛周围的四个小石像也开始活化。"

class PlotDescription {
  PlotDescription({
    this.content,
  });

  PlotDescription.fromJson(dynamic json) {
    content = json['content'];
  }

  String? content;

  PlotDescription copyWith({
    String? content,
  }) =>
      PlotDescription(
        content: content ?? this.content,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['content'] = content;
    return map;
  }
}
