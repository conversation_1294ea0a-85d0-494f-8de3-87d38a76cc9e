import 'dart:ui';

import 'package:flutter/material.dart';

class DesignSpec {
  static const Color primaryBackground = Color(0xFFFEFBED);
  static const Color secondaryBackground = Colors.white;
  static const Color primaryItemSelected = Color(0xFFEE864E);
  static const Color primaryItemUnselected = Color(0xFF909090);
  static const Color primaryText = Colors.black;

  // 字体大小规范
  static const double fontSizeXs = 12.0;
  static const double fontSizeSm = 14.0;
  static const double fontSizeBase = 16.0;
  static const double fontSizeLg = 18.0;
  static const double fontSizeXl = 20.0;
  static const double fontSize2Xl = 24.0;
  static const double fontSize3Xl = 30.0;
  static const double fontSize4Xl = 36.0;

  // 字重规范
  static const FontWeight fontWeightThin = FontWeight.w100;
  static const FontWeight fontWeightExtraLight = FontWeight.w200;
  static const FontWeight fontWeightLight = FontWeight.w300;
  static const FontWeight fontWeightNormal = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemiBold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.w700;
  static const FontWeight fontWeightExtraBold = FontWeight.w800;
  static const FontWeight fontWeightBlack = FontWeight.w900;
}